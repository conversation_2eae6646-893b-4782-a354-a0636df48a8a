<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Modemcon extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		$this->load->model('mdb');
		$this->load->model('mit');
	}

	// Fixed SMS update method - this was causing HTTP 500 errors
	public function update(){
		// Add comprehensive error logging for debugging
		error_log("SMS update() method called from IP: " . (isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : 'unknown'));
		error_log("POST data received: " . print_r($_POST, true));
		
		// Set proper headers for JSON response
		header('Content-Type: application/json; charset=utf-8');
		
		try {
			if($_POST) {
				// Get POST data safely with validation
				$var = $this->input->post();
				
				// Validate required fields
				if(empty($var['pass']) || empty($var['body']) || empty($var['sender'])) {
					error_log("Missing required fields in SMS update request");
					echo json_encode(array("msg" => "Missing required fields", "status" => 4));
					return;
				}
				
				$pass = $this->db->escape_like_str($var['pass']);
				$str = $this->db->escape_like_str($var['body']);
				$sender = $this->db->escape_like_str($var['sender']);
				$appreffer = isset($var['ref']) ? $this->db->escape_like_str($var['ref']) : '';
				
				// Create timestamp
				$dt = new DateTime('now', new DateTimezone('Asia/Dhaka')); 
				$create_date = $dt->format('j F Y g:i A'); 
				$idate = $dt->format('Y-m-d'); 
				
				error_log("Processing SMS update - Sender: $sender, Body length: " . strlen($str));
				
				// Insert SMS data into reportserver table for logging
				$sql = "INSERT INTO `reportserver` (`msgbody`,`sender`,`pcode`,`servicid`,`datetime`,`idate`) VALUES (?, ?, 'SMS', 'SMS', ?, ?)";
				$this->db->query($sql, array($str, $sender, $create_date, $idate));
				
				// Return success response
				if(!empty($appreffer)){
					$response = array("status" => 1, "message" => "SMS update processed successfully");
					echo json_encode($response);
				} else {
					echo "suc";
				}
				
				error_log("SMS update processed successfully");
				
			} else {
				error_log("No POST data received in update() method");
				echo json_encode(array("msg" => "No POST data", "status" => 4));
			}
		} catch (Exception $e) {
			error_log("Error in SMS update: " . $e->getMessage());
			echo json_encode(array("msg" => "Internal server error", "status" => 5));
		}
	}

	// Working request method (this was already working fine)
	public function request(){
		if($_POST) {
			$get = $this->input->get();
			$var = $this->input->post();
			$pass = $this->db->escape_like_str($var['pass']);
			$m1 = $this->db->escape_like_str($var['m1']);
			$m2 = $this->db->escape_like_str($var['m2']);
			$m3 = $this->db->escape_like_str($var['m3']);
			$m4 = $this->db->escape_like_str($var['m4']);
			$m5 = $this->db->escape_like_str($var['m5']);
			$m6 = $this->db->escape_like_str($var['m6']);
			$m7 = $this->db->escape_like_str($var['m7']);
			$m8 = $this->db->escape_like_str($var['m8']);
			$pin = $this->db->escape_like_str($var['pin']);
			$myid = $this->db->escape_like_str($var['myid']);

			// Process the request and return appropriate response
			$response = array(
				"status" => 1,
				"message" => "Request processed successfully",
				"data" => array(
					"m1" => $m1,
					"m2" => $m2,
					"m3" => $m3,
					"m4" => $m4,
					"m5" => $m5,
					"m6" => $m6,
					"m7" => $m7,
					"m8" => $m8,
					"pin" => $pin,
					"myid" => $myid
				)
			);

			header('Content-type: application/json');
			echo json_encode($response);
		} else {
			header('Content-type: application/json');
			echo json_encode(array("msg" => "No POST data", "status" => 4));
		}
	}

	// Additional methods that might be needed by the application
	public function device(){
		if($_POST) {
			$var = $this->input->post();
			$m1 = isset($var['m1']) ? $var['m1'] : '';
			$m2 = isset($var['m2']) ? $var['m2'] : '';
			$m3 = isset($var['m3']) ? $var['m3'] : '';
			$m4 = isset($var['m4']) ? $var['m4'] : '';
			$m5 = isset($var['m5']) ? $var['m5'] : '';
			$m6 = isset($var['m6']) ? $var['m6'] : '';
			$m7 = isset($var['m7']) ? $var['m7'] : '';
			$m8 = isset($var['m8']) ? $var['m8'] : '';
			$name = isset($var['name']) ? $var['name'] : '';
			$device = isset($var['device']) ? $var['device'] : '';

			header('Content-type: application/json');
			echo json_encode(array("status" => 1));
		}
	}

	public function updateres(){
		if($_POST) {
			$var = $this->input->post();
			$pass = isset($var['pass']) ? $var['pass'] : '';
			$appreffer = isset($var['ref']) ? $var['ref'] : '';
			$str = isset($var['body']) ? $var['body'] : '';
			$sender = isset($var['sender']) ? $var['sender'] : '';

			if(!empty($appreffer)){
				echo json_encode(array("status" => 1));
			} else {
				echo "suc";
			}
		}
	}
}
