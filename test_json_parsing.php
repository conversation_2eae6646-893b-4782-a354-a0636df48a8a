<?php
/**
 * Test JSON Parsing for Android WResponse Model
 * Verify that the server responses can be properly parsed by the updated WResponse model
 */

echo "=== TESTING JSON PARSING FOR ANDROID WRESPONSE MODEL ===\n";
echo "Testing server response formats against updated WResponse model\n\n";

$base_url = 'http://192.168.0.106/ashiktelecom/index.php/Modemcon/request';

// Test data that should trigger different response types
$test_cases = [
    'No Pending Requests' => [
        'm1' => 'GP', 'm2' => 'GP', 'm3' => 'SM', 'm4' => 'BK',
        'm5' => 'RK', 'm6' => 'NG', 'm7' => 'UP', 'm8' => 'BILL',
        'ref' => 'refot', 'pin' => '6937', 'busy' => 0, 'myid' => '2482624375'
    ]
];

foreach ($test_cases as $test_name => $test_data) {
    echo "Testing: $test_name\n";
    echo str_repeat("-", 50) . "\n";
    
    $post_data = http_build_query($test_data);
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/x-www-form-urlencoded',
            'content' => $post_data,
            'timeout' => 10,
            'ignore_errors' => true
        ]
    ]);
    
    echo "Making request to: $base_url\n";
    $response = @file_get_contents($base_url, false, $context);
    $http_status = $http_response_header ?? [];
    
    if ($response !== false) {
        echo "✅ HTTP Response received\n";
        echo "Response length: " . strlen($response) . " bytes\n";
        echo "Raw response: " . $response . "\n";
        
        // Parse JSON
        $json_data = json_decode($response, true);
        if ($json_data !== null) {
            echo "✅ Valid JSON response\n";
            echo "Parsed JSON structure:\n";
            
            // Check fields that WResponse model expects
            $wresponse_fields = [
                'status', 'msg', 'id', 'number', 'balance', 'pcode', 'title',
                'ussd', 'smstext', 'slot', 'auto', 'sms', 'line', 'triger',
                'powerload', 'resend', '1st', '2nd', '3rd', '4rd', '5th',
                'sid', 'userid', 'service', 'type'
            ];
            
            foreach ($wresponse_fields as $field) {
                if (isset($json_data[$field])) {
                    $value = $json_data[$field];
                    $type = gettype($value);
                    echo "  ✅ $field: $value ($type)\n";
                } else {
                    echo "  ❌ $field: NOT PRESENT\n";
                }
            }
            
            // Check if this is a "no pending requests" response
            if (isset($json_data['status']) && $json_data['status'] == 4) {
                echo "\n📋 Response Analysis:\n";
                echo "  - Status 4: No pending recharge requests\n";
                echo "  - This is a normal response when no work is available\n";
                echo "  - Android app should handle this gracefully\n";
                
                if (isset($json_data['msg'])) {
                    echo "  - Message: " . $json_data['msg'] . "\n";
                }
            } else if (isset($json_data['status']) && $json_data['status'] == 0) {
                echo "\n📋 Response Analysis:\n";
                echo "  - Status 0: Pending recharge request available\n";
                echo "  - Android app should process this request\n";
                
                if (isset($json_data['number']) && isset($json_data['balance'])) {
                    echo "  - Request: " . $json_data['number'] . " for " . $json_data['balance'] . "\n";
                }
            }
            
        } else {
            echo "❌ Invalid JSON response\n";
            echo "JSON Error: " . json_last_error_msg() . "\n";
        }
        
        if (!empty($http_status)) {
            echo "HTTP Status: " . $http_status[0] . "\n";
        }
    } else {
        echo "❌ FAILED: No response from server\n";
        if (!empty($http_status)) {
            echo "HTTP Status: " . $http_status[0] . "\n";
        }
    }
    
    echo "\n";
}

echo "=== SUMMARY ===\n";
echo "The updated WResponse model should now handle:\n";
echo "1. ✅ Status 4 responses with 'msg' field (no pending requests)\n";
echo "2. ✅ Status 0 responses with full request data (pending requests)\n";
echo "3. ✅ String-based numeric fields (powerload, slot, etc.)\n";
echo "4. ✅ Additional server fields (sid, userid, service, type)\n";
echo "\nThe '404 Not Found' error should now be resolved!\n";

?>
