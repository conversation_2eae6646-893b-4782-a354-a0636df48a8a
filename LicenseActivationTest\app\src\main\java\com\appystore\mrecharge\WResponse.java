package com.appystore.mrecharge;


import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class WResponse {
    @SerializedName("auto")
    @Expose
    private String auto; // Changed to String to match server response

    @SerializedName("balance")
    @Expose
    private String balance;

    @SerializedName("1st")
    @Expose
    private String first;

    @SerializedName("5th")
    @Expose
    private String five;

    @SerializedName("4rd")
    @Expose
    private String four;

    @SerializedName("id")
    @Expose
    private String id;

    @SerializedName("line")
    @Expose
    private String line; // Changed to String to match server response

    @SerializedName("number")
    @Expose
    private String number;

    @SerializedName("pcode")
    @Expose
    private String pcode;

    @SerializedName("powerload")
    @Expose
    private String powerload; // Changed to String to match server response

    @SerializedName("resend")
    @Expose
    private String resend; // Changed to String to match server response

    @SerializedName("2nd")
    @Expose
    private String second;

    @SerializedName("slot")
    @Expose
    private String slot; // Changed to String to match server response

    @SerializedName("sms")
    @Expose
    private String sms; // Changed to String to match server response

    @SerializedName("smstext")
    @Expose
    private String smstext;

    @SerializedName("status")
    @Expose
    private Object status; // Use Object to handle both Integer and String from server

    @SerializedName("3rd")
    @Expose
    private String three;

    @SerializedName("title")
    @Expose
    private String title;

    @SerializedName("triger")
    @Expose
    private String triger; // Changed to String to match server response

    @SerializedName("ussd")
    @Expose
    private String ussd;

    // Additional fields that the server returns but were missing
    @SerializedName("sid")
    @Expose
    private String sid;

    @SerializedName("userid")
    @Expose
    private String userid;

    @SerializedName("service")
    @Expose
    private String service;

    @SerializedName("type")
    @Expose
    private String type;

    // Field for "no pending requests" response format
    @SerializedName("msg")
    @Expose
    private String msg;

    public Integer getpowerload() {
        try {
            return this.powerload != null ? Integer.parseInt(this.powerload) : null;
        } catch (NumberFormatException e) {
            return null;
        }
    }

    public Integer getResend() {
        try {
            return this.resend != null ? Integer.parseInt(this.resend) : null;
        } catch (NumberFormatException e) {
            return null;
        }
    }

    public Integer getstatus() {
        if (this.status == null) {
            return null;
        }

        if (this.status instanceof Integer) {
            return (Integer) this.status;
        } else if (this.status instanceof String) {
            try {
                return Integer.parseInt((String) this.status);
            } catch (NumberFormatException e) {
                return null;
            }
        } else if (this.status instanceof Number) {
            return ((Number) this.status).intValue();
        }

        return null;
    }

    public void setstatus(Integer num) {
        this.status = num;
    }

    public String getnumber() {
        return this.number;
    }

    public String getbalance() {
        return this.balance;
    }

    public String getpcode() {
        return this.pcode;
    }

    public String gettitle() {
        return this.title;
    }

    public String getid() {
        return this.id;
    }

    public String getussd() {
        return this.ussd;
    }

    public String getsmstext() {
        return this.smstext;
    }

    public String getfirst() {
        return this.first;
    }

    public String getsecond() {
        return this.second;
    }

    public String getthree() {
        return this.three;
    }

    public String getfour() {
        return this.four;
    }

    public String getfive() {
        return this.five;
    }

    public Integer getslot() {
        try {
            return this.slot != null ? Integer.parseInt(this.slot) : null;
        } catch (NumberFormatException e) {
            return null;
        }
    }

    public Integer getauto() {
        try {
            return this.auto != null ? Integer.parseInt(this.auto) : null;
        } catch (NumberFormatException e) {
            return null;
        }
    }

    public Integer getsms() {
        try {
            return this.sms != null ? Integer.parseInt(this.sms) : null;
        } catch (NumberFormatException e) {
            return null;
        }
    }

    public Integer gettriger() {
        try {
            return this.triger != null ? Integer.parseInt(this.triger) : null;
        } catch (NumberFormatException e) {
            return null;
        }
    }

    public Integer getline() {
        try {
            return this.line != null ? Integer.parseInt(this.line) : null;
        } catch (NumberFormatException e) {
            return null;
        }
    }

    // Getter methods for the new fields
    public String getsid() {
        return this.sid;
    }

    public String getuserid() {
        return this.userid;
    }

    public String getservice() {
        return this.service;
    }

    public String gettype() {
        return this.type;
    }

    public String getmsg() {
        return this.msg;
    }
}
