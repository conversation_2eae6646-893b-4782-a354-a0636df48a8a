<?php
/**
 * Test script to verify the Modemcon/request endpoint is working
 * This simulates the Android app's POST request
 */

// Test data that matches what the Android app sends
$test_data = array(
    'm1' => 'GP',
    'm2' => 'GP', 
    'm3' => 'SM',
    'm4' => 'BK',
    'm5' => 'RK',
    'm6' => 'NG',
    'm7' => 'UP',
    'm8' => 'BILL',
    'pin' => '6937',
    'myid' => '2482624375'
);

// URL to test
$url = 'http://*************/ashiktelecom/index.php/Modemcon/request';

// Initialize cURL
$ch = curl_init();

// Set cURL options
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($test_data));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
    'Content-Type: application/x-www-form-urlencoded',
    'User-Agent: TestScript/1.0'
));
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);

// Execute the request
echo "Testing Modemcon/request endpoint...\n";
echo "URL: $url\n";
echo "POST Data: " . print_r($test_data, true) . "\n";

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curl_error = curl_error($ch);

curl_close($ch);

// Display results
echo "HTTP Status Code: $http_code\n";

if ($curl_error) {
    echo "cURL Error: $curl_error\n";
} else {
    echo "Response: $response\n";
    
    // Try to decode JSON response
    $json_response = json_decode($response, true);
    if ($json_response !== null) {
        echo "JSON Response (formatted):\n";
        echo json_encode($json_response, JSON_PRETTY_PRINT) . "\n";
        
        if (isset($json_response['status']) && $json_response['status'] == 1) {
            echo "✅ SUCCESS: Request processed successfully!\n";
        } else {
            echo "❌ ERROR: Request failed with status: " . ($json_response['status'] ?? 'unknown') . "\n";
        }
    } else {
        echo "❌ ERROR: Invalid JSON response\n";
    }
}

echo "\n--- Test Complete ---\n";
?>
